-- Fix Admin Dashboard RLS Policies and Database Functions
-- This script fixes all issues preventing the admin dashboard from fetching real-time data

-- ============================================================================
-- STEP 1: CHECK CURRENT STATE
-- ============================================================================

SELECT 'STEP 1: Checking current database state' as step;

-- Check if required tables exist
SELECT
  table_name,
  CASE
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = t.table_name)
    THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM (VALUES
  ('orders'),
  ('order_items'),
  ('products'),
  ('user_profiles'),
  ('categories'),
  ('completed_projects'),
  ('custom_projects')
) AS t(table_name);

-- Check current data counts
SELECT 'Current data counts:' as info;
SELECT
  (SELECT COUNT(*) FROM orders) as orders_count,
  (SELECT COUNT(*) FROM order_items) as order_items_count,
  (SELECT COUNT(*) FROM products) as products_count,
  (SELECT COUNT(*) FROM user_profiles) as user_profiles_count,
  (SELECT COUNT(*) FROM categories) as categories_count;

-- ============================================================================
-- STEP 2: FIX RLS POLICIES FOR ALL TABLES
-- ============================================================================

SELECT 'STEP 2: Fixing RLS policies for all dashboard tables' as step;

-- Enable RLS on all required tables
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create completed_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS completed_projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS custom_projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on project tables
ALTER TABLE completed_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_projects ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 3: DROP EXISTING POLICIES
-- ============================================================================

SELECT 'STEP 3: Dropping existing policies' as step;

-- Drop existing policies for orders
DROP POLICY IF EXISTS "Users can view their own orders" ON orders;
DROP POLICY IF EXISTS "Users can insert their own orders" ON orders;
DROP POLICY IF EXISTS "Admins can view all orders" ON orders;
DROP POLICY IF EXISTS "Admins can manage all orders" ON orders;

-- Drop existing policies for order_items
DROP POLICY IF EXISTS "Users can view their own order items" ON order_items;
DROP POLICY IF EXISTS "Users can insert their own order items" ON order_items;
DROP POLICY IF EXISTS "Admins can view all order items" ON order_items;
DROP POLICY IF EXISTS "Admins can manage all order items" ON order_items;

-- Drop existing policies for user_profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Public can view basic user info" ON user_profiles;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON user_profiles;

-- Drop existing policies for projects
DROP POLICY IF EXISTS "Admins can view completed projects" ON completed_projects;
DROP POLICY IF EXISTS "Admins can manage completed projects" ON completed_projects;
DROP POLICY IF EXISTS "Admins can view custom projects" ON custom_projects;
DROP POLICY IF EXISTS "Admins can manage custom projects" ON custom_projects;

-- ============================================================================
-- STEP 4: CREATE COMPREHENSIVE ADMIN POLICIES
-- ============================================================================

SELECT 'STEP 4: Creating comprehensive admin policies' as step;

-- Orders table policies
-- Users can view their own orders
CREATE POLICY "Users can view their own orders"
  ON orders FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert their own orders
CREATE POLICY "Users can insert their own orders"
  ON orders FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Admins can view ALL orders (for dashboard analytics)
CREATE POLICY "Admins can view all orders"
  ON orders FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all orders
CREATE POLICY "Admins can manage all orders"
  ON orders FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Order items table policies
-- Users can view their own order items
CREATE POLICY "Users can view their own order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM orders
      WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()
    )
  );

-- Admins can view ALL order items (for dashboard analytics)
CREATE POLICY "Admins can view all order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all order items
CREATE POLICY "Admins can manage all order items"
  ON order_items FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- User profiles policies
-- Users can view their own profile
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Admins can view ALL user profiles (for dashboard analytics)
CREATE POLICY "Admins can view all user profiles"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      WHERE up.id = auth.uid() AND up.role = 'admin'
    )
  );

-- Admins can manage all user profiles
CREATE POLICY "Admins can manage all user profiles"
  ON user_profiles FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      WHERE up.id = auth.uid() AND up.role = 'admin'
    )
  );

-- Project tables policies
-- Admins can view all completed projects
CREATE POLICY "Admins can view all completed projects"
  ON completed_projects FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all completed projects
CREATE POLICY "Admins can manage all completed projects"
  ON completed_projects FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can view all custom projects
CREATE POLICY "Admins can view all custom projects"
  ON custom_projects FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all custom projects
CREATE POLICY "Admins can manage all custom projects"
  ON custom_projects FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- STEP 5: CREATE MISSING DATABASE FUNCTIONS
-- ============================================================================

SELECT 'STEP 5: Creating missing database functions for analytics' as step;

-- Function to get total revenue from completed orders
CREATE OR REPLACE FUNCTION get_total_revenue()
RETURNS NUMERIC
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  total_revenue NUMERIC := 0;
BEGIN
  SELECT COALESCE(SUM(total_amount), 0)
  INTO total_revenue
  FROM orders
  WHERE status IN ('delivered', 'shipped', 'completed');

  RETURN total_revenue;
END;
$$;

-- Function to get monthly sales for the past 12 months
CREATE OR REPLACE FUNCTION get_monthly_sales()
RETURNS TABLE (
  month INTEGER,
  total_sales NUMERIC
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    EXTRACT(MONTH FROM created_at)::INTEGER as month,
    COALESCE(SUM(total_amount), 0) as total_sales
  FROM orders
  WHERE
    status IN ('delivered', 'shipped', 'completed') AND
    created_at >= NOW() - INTERVAL '12 months'
  GROUP BY EXTRACT(MONTH FROM created_at)
  ORDER BY month;
END;
$$;

-- Function to get weekly performance (last 8 weeks)
CREATE OR REPLACE FUNCTION get_weekly_performance()
RETURNS TABLE (
  week_start DATE,
  total_sales NUMERIC,
  order_count BIGINT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE_TRUNC('week', created_at)::DATE as week_start,
    COALESCE(SUM(total_amount), 0) as total_sales,
    COUNT(*) as order_count
  FROM orders
  WHERE
    status IN ('delivered', 'shipped', 'completed') AND
    created_at >= NOW() - INTERVAL '8 weeks'
  GROUP BY DATE_TRUNC('week', created_at)
  ORDER BY week_start;
END;
$$;

-- Function to get category performance
CREATE OR REPLACE FUNCTION get_category_performance()
RETURNS TABLE (
  category_name TEXT,
  total_sales NUMERIC,
  total_quantity BIGINT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(c.name, 'Uncategorized') as category_name,
    COALESCE(SUM(oi.price * oi.quantity), 0) as total_sales,
    COALESCE(SUM(oi.quantity), 0) as total_quantity
  FROM order_items oi
  JOIN orders o ON oi.order_id = o.id
  LEFT JOIN products p ON oi.product_id = p.id
  LEFT JOIN categories c ON p.category_id = c.id
  WHERE o.status IN ('delivered', 'shipped', 'completed')
  GROUP BY c.name
  ORDER BY total_sales DESC;
END;
$$;

-- Function to get top selling products
CREATE OR REPLACE FUNCTION get_top_selling_products(
  limit_count INTEGER DEFAULT 5,
  sort_by TEXT DEFAULT 'revenue'
)
RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  total_revenue NUMERIC,
  total_quantity BIGINT,
  product_image TEXT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  IF sort_by = 'quantity' THEN
    RETURN QUERY
    SELECT
      p.id as product_id,
      p.name as product_name,
      COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue,
      COALESCE(SUM(oi.quantity), 0) as total_quantity,
      COALESCE(
        (SELECT pi.image_url FROM product_images pi WHERE pi.product_id = p.id LIMIT 1),
        'https://placehold.co/100x100?text=No+Image'
      ) as product_image
    FROM products p
    LEFT JOIN order_items oi ON p.id = oi.product_id
    LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('delivered', 'shipped', 'completed')
    GROUP BY p.id, p.name
    ORDER BY total_quantity DESC
    LIMIT limit_count;
  ELSE
    RETURN QUERY
    SELECT
      p.id as product_id,
      p.name as product_name,
      COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue,
      COALESCE(SUM(oi.quantity), 0) as total_quantity,
      COALESCE(
        (SELECT pi.image_url FROM product_images pi WHERE pi.product_id = p.id LIMIT 1),
        'https://placehold.co/100x100?text=No+Image'
      ) as product_image
    FROM products p
    LEFT JOIN order_items oi ON p.id = oi.product_id
    LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('delivered', 'shipped', 'completed')
    GROUP BY p.id, p.name
    ORDER BY total_revenue DESC
    LIMIT limit_count;
  END IF;
END;
$$;

-- ============================================================================
-- STEP 6: GRANT PERMISSIONS FOR FUNCTIONS
-- ============================================================================

SELECT 'STEP 6: Granting permissions for database functions' as step;

-- Grant execute permissions on all functions to authenticated users
GRANT EXECUTE ON FUNCTION get_total_revenue() TO authenticated;
GRANT EXECUTE ON FUNCTION get_monthly_sales() TO authenticated;
GRANT EXECUTE ON FUNCTION get_weekly_performance() TO authenticated;
GRANT EXECUTE ON FUNCTION get_category_performance() TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_selling_products(INTEGER, TEXT) TO authenticated;

-- Grant basic table permissions
GRANT SELECT ON orders TO authenticated;
GRANT SELECT ON order_items TO authenticated;
GRANT SELECT ON products TO authenticated;
GRANT SELECT ON product_images TO authenticated;
GRANT SELECT ON categories TO authenticated;
GRANT SELECT ON user_profiles TO authenticated;
GRANT SELECT ON completed_projects TO authenticated;
GRANT SELECT ON custom_projects TO authenticated;

-- ============================================================================
-- STEP 7: CREATE SAMPLE DATA FOR TESTING
-- ============================================================================

SELECT 'STEP 7: Creating sample data for testing' as step;

-- Insert sample completed projects if table is empty
INSERT INTO completed_projects (name, description)
SELECT 'Sample Completed Project 1', 'A completed furniture project'
WHERE NOT EXISTS (SELECT 1 FROM completed_projects);

INSERT INTO completed_projects (name, description)
SELECT 'Sample Completed Project 2', 'Another completed project'
WHERE NOT EXISTS (SELECT 1 FROM completed_projects LIMIT 1 OFFSET 1);

-- Insert sample custom projects if table is empty
INSERT INTO custom_projects (name, description)
SELECT 'Sample Custom Project 1', 'A custom furniture design'
WHERE NOT EXISTS (SELECT 1 FROM custom_projects);

INSERT INTO custom_projects (name, description)
SELECT 'Sample Custom Project 2', 'Another custom design'
WHERE NOT EXISTS (SELECT 1 FROM custom_projects LIMIT 1 OFFSET 1);

-- ============================================================================
-- STEP 8: TEST ALL FUNCTIONS
-- ============================================================================

SELECT 'STEP 8: Testing all database functions' as step;

-- Test get_total_revenue function
SELECT 'Testing get_total_revenue:' as test, get_total_revenue() as result;

-- Test get_monthly_sales function
SELECT 'Testing get_monthly_sales:' as test;
SELECT * FROM get_monthly_sales() LIMIT 5;

-- Test get_weekly_performance function
SELECT 'Testing get_weekly_performance:' as test;
SELECT * FROM get_weekly_performance() LIMIT 5;

-- Test get_category_performance function
SELECT 'Testing get_category_performance:' as test;
SELECT * FROM get_category_performance() LIMIT 5;

-- Test get_top_selling_products function
SELECT 'Testing get_top_selling_products:' as test;
SELECT * FROM get_top_selling_products(5, 'revenue') LIMIT 5;

-- Test table access
SELECT 'Testing table access:' as test;
SELECT
  (SELECT COUNT(*) FROM orders) as orders_count,
  (SELECT COUNT(*) FROM order_items) as order_items_count,
  (SELECT COUNT(*) FROM products) as products_count,
  (SELECT COUNT(*) FROM user_profiles) as user_profiles_count,
  (SELECT COUNT(*) FROM completed_projects) as completed_projects_count,
  (SELECT COUNT(*) FROM custom_projects) as custom_projects_count;

SELECT 'Admin dashboard fix completed successfully!' as result;
