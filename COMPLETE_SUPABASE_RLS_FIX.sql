-- COMPLETE SUPABASE RLS POLICIES FIX
-- This script fixes ALL RLS policies for EVERY table in the application
-- Run this in Supabase SQL Editor to restore all data access

-- ============================================================================
-- STEP 1: CHECK ALL TABLES AND CURRENT STATE
-- ============================================================================

SELECT 'STEP 1: Checking all tables in the database' as step;

-- Check if all required tables exist
SELECT
  table_name,
  CASE
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = t.table_name)
    THEN '✅ EXISTS'
    ELSE '❌ MISSING'
  END as status
FROM (VALUES
  ('categories'),
  ('products'),
  ('product_images'),
  ('product_reviews'),
  ('orders'),
  ('order_items'),
  ('user_profiles'),
  ('user_addresses'),
  ('consultation_requests'),
  ('app_settings'),
  ('search_history'),
  ('employees'),
  ('attendance'),
  ('overtime'),
  ('payroll'),
  ('completed_projects'),
  ('custom_projects')
) AS t(table_name);

-- Check current data counts
SELECT 'Current data counts:' as info;
SELECT
  (SELECT COUNT(*) FROM categories) as categories_count,
  (SELECT COUNT(*) FROM products) as products_count,
  (SELECT COUNT(*) FROM product_images) as product_images_count,
  (SELECT COUNT(*) FROM orders) as orders_count,
  (SELECT COUNT(*) FROM user_profiles) as user_profiles_count;

-- ============================================================================
-- STEP 2: CREATE MISSING TABLES
-- ============================================================================

SELECT 'STEP 2: Creating missing tables if needed' as step;

-- Create completed_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS completed_projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS custom_projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: ENABLE RLS ON ALL TABLES
-- ============================================================================

SELECT 'STEP 3: Enabling RLS on all tables' as step;

-- Enable RLS on all tables
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultation_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE completed_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_projects ENABLE ROW LEVEL SECURITY;

-- Enable RLS on employee tables if they exist
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'employees') THEN
    ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attendance') THEN
    ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'overtime') THEN
    ALTER TABLE overtime ENABLE ROW LEVEL SECURITY;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payroll') THEN
    ALTER TABLE payroll ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- ============================================================================
-- STEP 4: DROP ALL EXISTING POLICIES
-- ============================================================================

SELECT 'STEP 4: Dropping all existing policies' as step;

-- Drop all existing policies for categories
DROP POLICY IF EXISTS "Public can view categories" ON categories;
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;

-- Drop all existing policies for products
DROP POLICY IF EXISTS "Public can view active products" ON products;
DROP POLICY IF EXISTS "Anyone can view active products" ON products;
DROP POLICY IF EXISTS "Admins can view all products" ON products;
DROP POLICY IF EXISTS "Admins can manage products" ON products;

-- Drop all existing policies for product_images
DROP POLICY IF EXISTS "Public can view product images" ON product_images;
DROP POLICY IF EXISTS "Anyone can view product images" ON product_images;
DROP POLICY IF EXISTS "Admins can manage product images" ON product_images;

-- Drop all existing policies for product_reviews
DROP POLICY IF EXISTS "Users can view all reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can create reviews for purchased products" ON product_reviews;
DROP POLICY IF EXISTS "Users can update their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can delete their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Admins can manage all reviews" ON product_reviews;

-- Drop all existing policies for orders
DROP POLICY IF EXISTS "Users can view their own orders" ON orders;
DROP POLICY IF EXISTS "Users can insert their own orders" ON orders;
DROP POLICY IF EXISTS "Admins can view all orders" ON orders;
DROP POLICY IF EXISTS "Admins can manage all orders" ON orders;

-- Drop all existing policies for order_items
DROP POLICY IF EXISTS "Users can view their own order items" ON order_items;
DROP POLICY IF EXISTS "Users can insert their own order items" ON order_items;
DROP POLICY IF EXISTS "Admins can view all order items" ON order_items;
DROP POLICY IF EXISTS "Admins can manage all order items" ON order_items;

-- Drop all existing policies for user_profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can manage all user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Public can view basic user info" ON user_profiles;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON user_profiles;

-- Drop all existing policies for user_addresses
DROP POLICY IF EXISTS "Users can view their own addresses" ON user_addresses;
DROP POLICY IF EXISTS "Users can insert their own addresses" ON user_addresses;
DROP POLICY IF EXISTS "Users can update their own addresses" ON user_addresses;
DROP POLICY IF EXISTS "Users can delete their own addresses" ON user_addresses;

-- Drop all existing policies for consultation_requests
DROP POLICY IF EXISTS "Anyone can submit a consultation request" ON consultation_requests;
DROP POLICY IF EXISTS "Admins can view all consultation requests" ON consultation_requests;

-- Drop all existing policies for app_settings
DROP POLICY IF EXISTS "Anyone can view app settings" ON app_settings;
DROP POLICY IF EXISTS "Only admins can modify app settings" ON app_settings;

-- Drop all existing policies for search_history
DROP POLICY IF EXISTS "insert_own_search_history" ON search_history;
DROP POLICY IF EXISTS "view_own_search_history" ON search_history;

-- Drop all existing policies for project tables
DROP POLICY IF EXISTS "Admins can view all completed projects" ON completed_projects;
DROP POLICY IF EXISTS "Admins can manage all completed projects" ON completed_projects;
DROP POLICY IF EXISTS "Admins can view all custom projects" ON custom_projects;
DROP POLICY IF EXISTS "Admins can manage all custom projects" ON custom_projects;

-- Drop employee table policies if they exist
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'employees') THEN
    DROP POLICY IF EXISTS "Only admins can view employees" ON employees;
    DROP POLICY IF EXISTS "Only admins can insert employees" ON employees;
    DROP POLICY IF EXISTS "Only admins can update employees" ON employees;
    DROP POLICY IF EXISTS "Only admins can delete employees" ON employees;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attendance') THEN
    DROP POLICY IF EXISTS "Only admins can view attendance" ON attendance;
    DROP POLICY IF EXISTS "Only admins can manage attendance" ON attendance;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'overtime') THEN
    DROP POLICY IF EXISTS "Only admins can view overtime" ON overtime;
    DROP POLICY IF EXISTS "Only admins can manage overtime" ON overtime;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payroll') THEN
    DROP POLICY IF EXISTS "Only admins can view payroll" ON payroll;
    DROP POLICY IF EXISTS "Only admins can manage payroll" ON payroll;
  END IF;
END $$;

-- ============================================================================
-- STEP 5: CREATE COMPREHENSIVE RLS POLICIES
-- ============================================================================

SELECT 'STEP 5: Creating comprehensive RLS policies for all tables' as step;

-- ============================================================================
-- CATEGORIES TABLE POLICIES
-- ============================================================================

-- Anyone can view categories (public access)
CREATE POLICY "Public can view categories"
  ON categories FOR SELECT
  USING (true);

-- Admins can manage categories
CREATE POLICY "Admins can manage categories"
  ON categories FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- PRODUCTS TABLE POLICIES
-- ============================================================================

-- Anyone can view active products (public access)
CREATE POLICY "Public can view active products"
  ON products FOR SELECT
  USING (status = 'active');

-- Admins can view all products (including drafts)
CREATE POLICY "Admins can view all products"
  ON products FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all products
CREATE POLICY "Admins can manage products"
  ON products FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- PRODUCT_IMAGES TABLE POLICIES
-- ============================================================================

-- Anyone can view product images (public access)
CREATE POLICY "Public can view product images"
  ON product_images FOR SELECT
  USING (true);

-- Admins can manage product images
CREATE POLICY "Admins can manage product images"
  ON product_images FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- PRODUCT_REVIEWS TABLE POLICIES
-- ============================================================================

-- Anyone can view all reviews (public access)
CREATE POLICY "Public can view all reviews"
  ON product_reviews FOR SELECT
  USING (true);

-- Authenticated users can create reviews
CREATE POLICY "Users can create reviews"
  ON product_reviews FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own reviews
CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Admins can manage all reviews
CREATE POLICY "Admins can manage all reviews"
  ON product_reviews FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- ORDERS TABLE POLICIES
-- ============================================================================

-- Users can view their own orders
CREATE POLICY "Users can view their own orders"
  ON orders FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert their own orders
CREATE POLICY "Users can insert their own orders"
  ON orders FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own orders (limited)
CREATE POLICY "Users can update their own orders"
  ON orders FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Admins can view all orders
CREATE POLICY "Admins can view all orders"
  ON orders FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all orders
CREATE POLICY "Admins can manage all orders"
  ON orders FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- ORDER_ITEMS TABLE POLICIES
-- ============================================================================

-- Users can view their own order items
CREATE POLICY "Users can view their own order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM orders
      WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()
    )
  );

-- Users can insert their own order items
CREATE POLICY "Users can insert their own order items"
  ON order_items FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM orders
      WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()
    )
  );

-- Admins can view all order items
CREATE POLICY "Admins can view all order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all order items
CREATE POLICY "Admins can manage all order items"
  ON order_items FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- USER_PROFILES TABLE POLICIES
-- ============================================================================

-- Users can view their own profile
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert their own profile"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Admins can view all user profiles
CREATE POLICY "Admins can view all user profiles"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      WHERE up.id = auth.uid() AND up.role = 'admin'
    )
  );

-- Admins can manage all user profiles
CREATE POLICY "Admins can manage all user profiles"
  ON user_profiles FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      WHERE up.id = auth.uid() AND up.role = 'admin'
    )
  );

-- ============================================================================
-- USER_ADDRESSES TABLE POLICIES
-- ============================================================================

-- Users can view their own addresses
CREATE POLICY "Users can view their own addresses"
  ON user_addresses FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert their own addresses
CREATE POLICY "Users can insert their own addresses"
  ON user_addresses FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own addresses
CREATE POLICY "Users can update their own addresses"
  ON user_addresses FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can delete their own addresses
CREATE POLICY "Users can delete their own addresses"
  ON user_addresses FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Admins can view all addresses
CREATE POLICY "Admins can view all addresses"
  ON user_addresses FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- CONSULTATION_REQUESTS TABLE POLICIES
-- ============================================================================

-- Anyone can submit a consultation request
CREATE POLICY "Anyone can submit consultation request"
  ON consultation_requests FOR INSERT
  USING (true);

-- Users can view their own consultation requests
CREATE POLICY "Users can view their own consultation requests"
  ON consultation_requests FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR user_id IS NULL);

-- Admins can view all consultation requests
CREATE POLICY "Admins can view all consultation requests"
  ON consultation_requests FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all consultation requests
CREATE POLICY "Admins can manage all consultation requests"
  ON consultation_requests FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- APP_SETTINGS TABLE POLICIES
-- ============================================================================

-- Anyone can view app settings (public access)
CREATE POLICY "Public can view app settings"
  ON app_settings FOR SELECT
  USING (true);

-- Only admins can modify app settings
CREATE POLICY "Admins can manage app settings"
  ON app_settings FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- SEARCH_HISTORY TABLE POLICIES
-- ============================================================================

-- Users can view their own search history
CREATE POLICY "Users can view their own search history"
  ON search_history FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Users can insert their own search history
CREATE POLICY "Users can insert their own search history"
  ON search_history FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can delete their own search history
CREATE POLICY "Users can delete their own search history"
  ON search_history FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- ============================================================================
-- PROJECT TABLES POLICIES
-- ============================================================================

-- Admins can view all completed projects
CREATE POLICY "Admins can view all completed projects"
  ON completed_projects FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all completed projects
CREATE POLICY "Admins can manage all completed projects"
  ON completed_projects FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can view all custom projects
CREATE POLICY "Admins can view all custom projects"
  ON custom_projects FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all custom projects
CREATE POLICY "Admins can manage all custom projects"
  ON custom_projects FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- EMPLOYEE TABLES POLICIES (IF THEY EXIST)
-- ============================================================================

-- Create policies for employee tables if they exist
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'employees') THEN
    -- Admins can view all employees
    EXECUTE 'CREATE POLICY "Admins can view all employees"
      ON employees FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';

    -- Admins can manage all employees
    EXECUTE 'CREATE POLICY "Admins can manage all employees"
      ON employees FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';
  END IF;

  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attendance') THEN
    -- Admins can view all attendance
    EXECUTE 'CREATE POLICY "Admins can view all attendance"
      ON attendance FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';

    -- Admins can manage all attendance
    EXECUTE 'CREATE POLICY "Admins can manage all attendance"
      ON attendance FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';
  END IF;

  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'overtime') THEN
    -- Admins can view all overtime
    EXECUTE 'CREATE POLICY "Admins can view all overtime"
      ON overtime FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';

    -- Admins can manage all overtime
    EXECUTE 'CREATE POLICY "Admins can manage all overtime"
      ON overtime FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';
  END IF;

  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payroll') THEN
    -- Admins can view all payroll
    EXECUTE 'CREATE POLICY "Admins can view all payroll"
      ON payroll FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';

    -- Admins can manage all payroll
    EXECUTE 'CREATE POLICY "Admins can manage all payroll"
      ON payroll FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE id = auth.uid() AND role = ''admin''
        )
      )';
  END IF;
END $$;

-- ============================================================================
-- STEP 6: CREATE MISSING DATABASE FUNCTIONS FOR ANALYTICS
-- ============================================================================

SELECT 'STEP 6: Creating missing database functions for analytics' as step;

-- Function to get total revenue from completed orders
CREATE OR REPLACE FUNCTION get_total_revenue()
RETURNS NUMERIC
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  total_revenue NUMERIC := 0;
BEGIN
  SELECT COALESCE(SUM(total_amount), 0)
  INTO total_revenue
  FROM orders
  WHERE status IN ('delivered', 'shipped', 'completed');

  RETURN total_revenue;
END;
$$;

-- Function to get monthly sales for the past 12 months
CREATE OR REPLACE FUNCTION get_monthly_sales()
RETURNS TABLE (
  month INTEGER,
  total_sales NUMERIC
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    EXTRACT(MONTH FROM created_at)::INTEGER as month,
    COALESCE(SUM(total_amount), 0) as total_sales
  FROM orders
  WHERE
    status IN ('delivered', 'shipped', 'completed') AND
    created_at >= NOW() - INTERVAL '12 months'
  GROUP BY EXTRACT(MONTH FROM created_at)
  ORDER BY month;
END;
$$;

-- Function to get weekly performance (last 8 weeks)
CREATE OR REPLACE FUNCTION get_weekly_performance()
RETURNS TABLE (
  week_start DATE,
  total_sales NUMERIC,
  order_count BIGINT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE_TRUNC('week', created_at)::DATE as week_start,
    COALESCE(SUM(total_amount), 0) as total_sales,
    COUNT(*) as order_count
  FROM orders
  WHERE
    status IN ('delivered', 'shipped', 'completed') AND
    created_at >= NOW() - INTERVAL '8 weeks'
  GROUP BY DATE_TRUNC('week', created_at)
  ORDER BY week_start;
END;
$$;

-- Function to get category performance
CREATE OR REPLACE FUNCTION get_category_performance()
RETURNS TABLE (
  category_name TEXT,
  total_sales NUMERIC,
  total_quantity BIGINT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(c.name, 'Uncategorized') as category_name,
    COALESCE(SUM(oi.price * oi.quantity), 0) as total_sales,
    COALESCE(SUM(oi.quantity), 0) as total_quantity
  FROM order_items oi
  JOIN orders o ON oi.order_id = o.id
  LEFT JOIN products p ON oi.product_id = p.id
  LEFT JOIN categories c ON p.category_id = c.id
  WHERE o.status IN ('delivered', 'shipped', 'completed')
  GROUP BY c.name
  ORDER BY total_sales DESC;
END;
$$;

-- Function to get top selling products
CREATE OR REPLACE FUNCTION get_top_selling_products(
  limit_count INTEGER DEFAULT 5,
  sort_by TEXT DEFAULT 'revenue'
)
RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  total_revenue NUMERIC,
  total_quantity BIGINT,
  product_image TEXT
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  IF sort_by = 'quantity' THEN
    RETURN QUERY
    SELECT
      p.id as product_id,
      p.name as product_name,
      COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue,
      COALESCE(SUM(oi.quantity), 0) as total_quantity,
      COALESCE(
        (SELECT pi.image_url FROM product_images pi WHERE pi.product_id = p.id LIMIT 1),
        'https://placehold.co/100x100?text=No+Image'
      ) as product_image
    FROM products p
    LEFT JOIN order_items oi ON p.id = oi.product_id
    LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('delivered', 'shipped', 'completed')
    GROUP BY p.id, p.name
    ORDER BY total_quantity DESC
    LIMIT limit_count;
  ELSE
    RETURN QUERY
    SELECT
      p.id as product_id,
      p.name as product_name,
      COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue,
      COALESCE(SUM(oi.quantity), 0) as total_quantity,
      COALESCE(
        (SELECT pi.image_url FROM product_images pi WHERE pi.product_id = p.id LIMIT 1),
        'https://placehold.co/100x100?text=No+Image'
      ) as product_image
    FROM products p
    LEFT JOIN order_items oi ON p.id = oi.product_id
    LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('delivered', 'shipped', 'completed')
    GROUP BY p.id, p.name
    ORDER BY total_revenue DESC
    LIMIT limit_count;
  END IF;
END;
$$;

-- ============================================================================
-- STEP 7: GRANT COMPREHENSIVE PERMISSIONS
-- ============================================================================

SELECT 'STEP 7: Granting comprehensive permissions' as step;

-- Grant execute permissions on all functions to authenticated users
GRANT EXECUTE ON FUNCTION get_total_revenue() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_monthly_sales() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_weekly_performance() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_category_performance() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_top_selling_products(INTEGER, TEXT) TO authenticated, anon;

-- Grant basic table permissions
GRANT SELECT ON categories TO authenticated, anon;
GRANT SELECT ON products TO authenticated, anon;
GRANT SELECT ON product_images TO authenticated, anon;
GRANT SELECT ON product_reviews TO authenticated, anon;
GRANT SELECT ON orders TO authenticated;
GRANT SELECT ON order_items TO authenticated;
GRANT SELECT ON user_profiles TO authenticated;
GRANT SELECT ON user_addresses TO authenticated;
GRANT SELECT ON consultation_requests TO authenticated, anon;
GRANT SELECT ON app_settings TO authenticated, anon;
GRANT SELECT ON search_history TO authenticated;
GRANT SELECT ON completed_projects TO authenticated;
GRANT SELECT ON custom_projects TO authenticated;

-- Grant insert/update/delete permissions where appropriate
GRANT INSERT ON categories TO authenticated;
GRANT INSERT, UPDATE, DELETE ON products TO authenticated;
GRANT INSERT, UPDATE, DELETE ON product_images TO authenticated;
GRANT INSERT, UPDATE, DELETE ON product_reviews TO authenticated;
GRANT INSERT, UPDATE, DELETE ON orders TO authenticated;
GRANT INSERT, UPDATE, DELETE ON order_items TO authenticated;
GRANT INSERT, UPDATE, DELETE ON user_profiles TO authenticated;
GRANT INSERT, UPDATE, DELETE ON user_addresses TO authenticated;
GRANT INSERT ON consultation_requests TO authenticated, anon;
GRANT INSERT, UPDATE, DELETE ON app_settings TO authenticated;
GRANT INSERT, UPDATE, DELETE ON search_history TO authenticated;
GRANT INSERT, UPDATE, DELETE ON completed_projects TO authenticated;
GRANT INSERT, UPDATE, DELETE ON custom_projects TO authenticated;

-- Grant permissions on employee tables if they exist
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'employees') THEN
    GRANT SELECT, INSERT, UPDATE, DELETE ON employees TO authenticated;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'attendance') THEN
    GRANT SELECT, INSERT, UPDATE, DELETE ON attendance TO authenticated;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'overtime') THEN
    GRANT SELECT, INSERT, UPDATE, DELETE ON overtime TO authenticated;
  END IF;
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payroll') THEN
    GRANT SELECT, INSERT, UPDATE, DELETE ON payroll TO authenticated;
  END IF;
END $$;

-- ============================================================================
-- STEP 8: CREATE SAMPLE DATA FOR TESTING
-- ============================================================================

SELECT 'STEP 8: Creating sample data for testing' as step;

-- Insert sample completed projects if table is empty
INSERT INTO completed_projects (name, description)
SELECT 'Sample Completed Project 1', 'A completed furniture project'
WHERE NOT EXISTS (SELECT 1 FROM completed_projects);

INSERT INTO completed_projects (name, description)
SELECT 'Sample Completed Project 2', 'Another completed project'
WHERE NOT EXISTS (SELECT 1 FROM completed_projects LIMIT 1 OFFSET 1);

-- Insert sample custom projects if table is empty
INSERT INTO custom_projects (name, description)
SELECT 'Sample Custom Project 1', 'A custom furniture design'
WHERE NOT EXISTS (SELECT 1 FROM custom_projects);

INSERT INTO custom_projects (name, description)
SELECT 'Sample Custom Project 2', 'Another custom design'
WHERE NOT EXISTS (SELECT 1 FROM custom_projects LIMIT 1 OFFSET 1);

-- ============================================================================
-- STEP 9: TEST ALL FUNCTIONS AND TABLE ACCESS
-- ============================================================================

SELECT 'STEP 9: Testing all database functions and table access' as step;

-- Test get_total_revenue function
SELECT 'Testing get_total_revenue:' as test, get_total_revenue() as result;

-- Test get_monthly_sales function
SELECT 'Testing get_monthly_sales:' as test;
SELECT * FROM get_monthly_sales() LIMIT 5;

-- Test get_weekly_performance function
SELECT 'Testing get_weekly_performance:' as test;
SELECT * FROM get_weekly_performance() LIMIT 5;

-- Test get_category_performance function
SELECT 'Testing get_category_performance:' as test;
SELECT * FROM get_category_performance() LIMIT 5;

-- Test get_top_selling_products function
SELECT 'Testing get_top_selling_products:' as test;
SELECT * FROM get_top_selling_products(5, 'revenue') LIMIT 5;

-- Test table access
SELECT 'Testing table access:' as test;
SELECT
  (SELECT COUNT(*) FROM categories) as categories_count,
  (SELECT COUNT(*) FROM products) as products_count,
  (SELECT COUNT(*) FROM product_images) as product_images_count,
  (SELECT COUNT(*) FROM product_reviews) as product_reviews_count,
  (SELECT COUNT(*) FROM orders) as orders_count,
  (SELECT COUNT(*) FROM order_items) as order_items_count,
  (SELECT COUNT(*) FROM user_profiles) as user_profiles_count,
  (SELECT COUNT(*) FROM completed_projects) as completed_projects_count,
  (SELECT COUNT(*) FROM custom_projects) as custom_projects_count;

-- ============================================================================
-- FINAL RESULT
-- ============================================================================

SELECT '🎉 COMPLETE SUPABASE RLS FIX COMPLETED SUCCESSFULLY! 🎉' as result;
SELECT 'All RLS policies have been recreated for every table in the application.' as info;
SELECT 'All database functions for analytics have been created.' as info2;
SELECT 'All permissions have been granted appropriately.' as info3;
SELECT 'Your application should now have full access to all Supabase data!' as info4;