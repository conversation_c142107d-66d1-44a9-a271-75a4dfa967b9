# 🚨 URGENT FIX: Infinite Recursion in RLS Policies

## ⚡ **IMMEDIATE ACTION REQUIRED**

Your console shows **"Infinite recursion detected in policy for relation 'user_profiles'"** which is breaking your entire application.

## 🎯 **Root Cause**
The RLS policies on `user_profiles` table are trying to check if a user is admin by querying the same `user_profiles` table, creating an infinite loop.

## 🔧 **URGENT FIX STEPS**

### Step 1: Apply the Fix Immediately
1. **Open Supabase Dashboard**: https://tfvbwveohcbghqmxnpbd.supabase.co
2. **Go to SQL Editor**
3. **Copy ALL content** from `URGENT_FIX_INFINITE_RECURSION.sql`
4. **Paste and RUN** immediately
5. **Wait for completion** (should be fast, ~10 seconds)

### Step 2: Verify Success
Look for these messages in the output:
- ✅ "URGENT FIX: Disabling RLS temporarily to break infinite recursion"
- ✅ "STEP 2: Dropping all problematic user_profiles policies"
- ✅ "STEP 3: Creating helper function to check admin status safely"
- ✅ "STEP 4: Creating safe, non-recursive RLS policies"
- ✅ "🎉 URGENT FIX COMPLETED! 🎉"

### Step 3: Test Immediately
1. **Refresh your website** (hard refresh: Ctrl+F5)
2. **Check browser console** - errors should be gone
3. **Test homepage** - products should load
4. **Test admin dashboard** - should work without errors

## 🔍 **What This Fix Does**

### ✅ **Breaks the Infinite Loop**
- Temporarily disables RLS on user_profiles
- Drops all problematic recursive policies
- Creates a safe `is_admin()` function that doesn't cause recursion

### ✅ **Creates Safe Policies**
- Users can view/edit their own profiles (no recursion)
- Admins can manage all profiles (using safe function)
- All other tables use the safe admin check function

### ✅ **Fixes Dependent Services**
- Products service will work
- Orders service will work  
- Cart service will work
- User profile service will work
- Admin dashboard will work

## 🎯 **Expected Results After Fix**

### ✅ **Console Errors Gone**
- No more "Infinite recursion" errors
- No more "Internal Server Error" messages
- Clean console output

### ✅ **Services Working**
- Products loading on homepage
- Admin dashboard showing data
- User profiles accessible
- Orders management working
- Cart functionality restored

### ✅ **All Features Restored**
- Login/signup working
- Profile management working
- Product browsing working
- Admin functions working

## 🚨 **Why This Happened**

The original RLS policies were written like this:
```sql
-- PROBLEMATIC (causes infinite recursion)
CREATE POLICY "Admins can view all profiles"
  ON user_profiles FOR SELECT
  USING ((SELECT role FROM user_profiles WHERE id = auth.uid()) = 'admin');
```

This creates recursion because:
1. Policy tries to check if user is admin
2. To check admin status, it queries user_profiles table
3. Querying user_profiles triggers the same policy
4. Infinite loop occurs

## ✅ **How the Fix Solves It**

The fix creates a `SECURITY DEFINER` function:
```sql
-- SAFE (no recursion)
CREATE FUNCTION is_admin(user_id UUID) RETURNS BOOLEAN
SECURITY DEFINER -- This bypasses RLS
```

Now policies use:
```sql
-- SAFE (uses function that bypasses RLS)
CREATE POLICY "Admins can view all profiles"
  ON user_profiles FOR SELECT
  USING (is_admin(auth.uid()));
```

## 🎉 **Success Indicators**

You'll know it worked when:
1. **Browser console is clean** (no red errors)
2. **Homepage loads products** properly
3. **Admin dashboard shows data** instead of errors
4. **All user functions work** (login, profile, cart)
5. **No more 500 Internal Server Errors**

## ⚡ **APPLY THIS FIX NOW**

This is a critical issue that's breaking your entire application. Run the `URGENT_FIX_INFINITE_RECURSION.sql` script immediately to restore functionality.

After applying this fix, your application should work normally again!
