/**
 * Customer and Product Analytics Service
 *
 * This module provides functions to fetch customer and product-related analytics data.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError } from '@/utils/supabaseHelpers';
import { CategoryPerformance, CustomerStats, TopProductsSortBy, TopSellingProduct } from './types';

/**
 * Get customer statistics (total, active, inactive)
 * @returns Customer statistics
 */
export const getCustomerStats = async (): Promise<CustomerStats> => {
  try {
    // Initialize default values
    let total = 0;
    let active = 0;
    let inactive = 0;
    let totalCustomers = 0;

    try {
      // Get total customers (users with role 'user')
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'user');

      if (!error) {
        total = count || 0;
      } else {
        console.error('Error fetching total users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching total users:', err);
    }

    // Note: Active/Inactive user tracking has been removed as it's no longer needed
    // Setting default values for backward compatibility
    active = 0;
    inactive = 0;

    try {
      // Get total customers (all users regardless of role)
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true });

      if (!error) {
        totalCustomers = count || 0;
      } else {
        console.error('Error fetching all users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching all users:', err);
    }

    return {
      total,
      active,
      inactive,
      totalCustomers
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error in getCustomerStats:', errorMessage);
    handleSupabaseError(error, 'getCustomerStats', false);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      totalCustomers: 0
    };
  }
};

/**
 * Get category performance (sales by category)
 * @returns Category performance data
 */
export const getCategoryPerformance = async (): Promise<CategoryPerformance[]> => {
  try {
    const { data, error } = await supabase.rpc('get_category_performance');

    if (error) {
      console.error('Error fetching category performance:', error);
      return [];
    }

    return data.map((item: any) => ({
      name: item.category_name,
      value: parseFloat(String(item.total_sales))
    }));
  } catch (error) {
    handleSupabaseError(error, 'getCategoryPerformance', false);
    return [];
  }
};

/**
 * Get top selling products by revenue or quantity
 * @param limit Number of products to return
 * @param sortBy Sort by revenue or quantity
 * @returns Top selling products data
 */
export const getTopSellingProducts = async (
  limit: number = 5,
  sortBy: TopProductsSortBy = 'revenue'
): Promise<TopSellingProduct[]> => {
  try {
    // Use the single function with parameters
    const { data, error } = await supabase.rpc('get_top_selling_products', {
      limit_count: limit,
      sort_by: sortBy
    });

    if (error) {
      console.error(`Error fetching top selling products by ${sortBy}:`, error);
      return [];
    }

    return data.map((item: any) => ({
      id: item.product_id,
      name: item.product_name,
      category: 'General', // Default category since we don't have it in the function
      total_sales: parseFloat(String(item.total_revenue || 0)),
      quantity_sold: parseInt(String(item.total_quantity || 0)),
      image_url: item.product_image || 'https://placehold.co/100x100?text=No+Image'
    }));
  } catch (error) {
    handleSupabaseError(error, `getTopSellingProducts (${sortBy})`, false);
    return [];
  }
};

/**
 * Get total products count
 * @returns Total products count
 */
export const getTotalProductsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (error) {
      console.error('Error fetching total products count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    handleSupabaseError(error, 'getTotalProductsCount', false);
    return 0;
  }
};

/**
 * Get total projects count
 * @returns Total projects count
 */
export const getTotalProjectsCount = async (): Promise<number> => {
  try {
    // Count completed projects
    const { count: completedCount, error: completedError } = await supabase
      .from('completed_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (completedError) {
      console.error('Error fetching completed projects count:', completedError);
      return 0;
    }

    // Count custom projects
    const { count: customCount, error: customError } = await supabase
      .from('custom_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (customError) {
      console.error('Error fetching custom projects count:', customError);
      return 0;
    }

    return (completedCount || 0) + (customCount || 0);
  } catch (error) {
    handleSupabaseError(error, 'getTotalProjectsCount', false);
    return 0;
  }
};
