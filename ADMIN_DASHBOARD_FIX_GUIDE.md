# Admin Dashboard Real-Time Data Fix Guide

## Problem
The admin dashboard is showing static/mock data instead of real-time data from Supabase. This is caused by:

1. **Missing Database Functions**: Functions like `get_total_revenue`, `get_monthly_sales`, etc. don't exist
2. **RLS Policy Issues**: Row Level Security policies are blocking admin access to required tables
3. **Missing Project Tables**: `completed_projects` and `custom_projects` tables don't exist
4. **Service Function Mismatches**: Frontend services calling wrong function names

## Solution Overview

### Step 1: Run the Database Fix Script

**File**: `fix_admin_dashboard_rls_and_functions.sql`

**What it does**:
- ✅ Creates missing database functions for analytics
- ✅ Fixes RLS policies for all dashboard tables
- ✅ Creates missing project tables
- ✅ Grants proper permissions
- ✅ Tests all functions

**How to run**:
1. Open Supabase Dashboard
2. Go to SQL Editor
3. Copy and paste the entire content of `fix_admin_dashboard_rls_and_functions.sql`
4. Click "Run"
5. Check for any errors in the output

### Step 2: Frontend Service Updates

**Files Updated**:
- `src/services/analytics/customerProductService.ts`
- `src/services/analytics/revenueService.ts`

**Changes Made**:
- ✅ Updated `getCategoryPerformance()` to use `get_category_performance`
- ✅ Updated `getTopSellingProducts()` to use correct function signature
- ✅ Updated `getWeeklyPerformance()` to use `get_weekly_performance`

### Step 3: Database Functions Created

1. **`get_total_revenue()`**
   - Returns total revenue from completed orders
   - Used by: Revenue card on dashboard

2. **`get_monthly_sales()`**
   - Returns monthly sales for past 12 months
   - Used by: Sales trend chart

3. **`get_weekly_performance()`**
   - Returns weekly performance for past 8 weeks
   - Used by: Weekly performance chart

4. **`get_category_performance()`**
   - Returns sales performance by category
   - Used by: Category performance chart

5. **`get_top_selling_products(limit, sort_by)`**
   - Returns top selling products by revenue or quantity
   - Used by: Top selling products section

### Step 4: RLS Policies Fixed

**Tables with Updated Policies**:
- `orders` - Admins can view all orders
- `order_items` - Admins can view all order items
- `user_profiles` - Admins can view all user profiles
- `products` - Already has proper policies
- `categories` - Already has proper policies
- `completed_projects` - New table with admin policies
- `custom_projects` - New table with admin policies

### Step 5: Testing

After running the SQL script, test these queries in Supabase SQL Editor:

```sql
-- Test all functions
SELECT get_total_revenue() as total_revenue;
SELECT * FROM get_monthly_sales() LIMIT 5;
SELECT * FROM get_weekly_performance() LIMIT 5;
SELECT * FROM get_category_performance() LIMIT 5;
SELECT * FROM get_top_selling_products(5, 'revenue') LIMIT 5;

-- Test table access
SELECT COUNT(*) FROM orders;
SELECT COUNT(*) FROM order_items;
SELECT COUNT(*) FROM user_profiles;
SELECT COUNT(*) FROM completed_projects;
SELECT COUNT(*) FROM custom_projects;
```

## Expected Results

After applying the fix:

1. **Dashboard Cards**: Should show real counts and revenue
2. **Charts**: Should display actual data from database
3. **Real-time Updates**: Dashboard should update when new orders are placed
4. **No Errors**: Console should be free of RLS or function errors

## Troubleshooting

### Issue: Functions still not found
**Solution**: Make sure you ran the entire SQL script without errors

### Issue: Permission denied errors
**Solution**: Check that your user has admin role in `user_profiles` table

### Issue: Empty data showing
**Solution**: 
1. Check if you have actual orders in the database
2. Verify order statuses are 'delivered', 'shipped', or 'completed'
3. Run the test queries to verify data exists

### Issue: Charts not updating
**Solution**: 
1. Clear browser cache
2. Check browser console for errors
3. Verify React Query is refetching data

## Verification Steps

1. **Login as Admin**: Make sure you're logged in with admin privileges
2. **Check Dashboard**: All cards should show real numbers
3. **Check Charts**: All charts should display actual data
4. **Test Real-time**: Place a test order and verify dashboard updates
5. **Check Console**: No errors should appear in browser console

## Files Modified

- ✅ `fix_admin_dashboard_rls_and_functions.sql` (new)
- ✅ `src/services/analytics/customerProductService.ts` (updated)
- ✅ `src/services/analytics/revenueService.ts` (updated)
- ✅ `ADMIN_DASHBOARD_FIX_GUIDE.md` (new)

## Next Steps

After confirming the fix works:
1. Test all dashboard functionality
2. Verify real-time updates work
3. Check that admin orders management also works
4. Test with different user roles to ensure security
