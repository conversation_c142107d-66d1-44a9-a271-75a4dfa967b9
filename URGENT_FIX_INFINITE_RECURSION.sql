-- URGENT FIX: Infinite Recursion in user_profiles RLS Policies
-- This script fixes the infinite recursion issue causing all services to fail

-- ============================================================================
-- STEP 1: DISABLE RLS TEMPORARILY TO BREAK THE RECURSION
-- ============================================================================

SELECT 'URGENT FIX: Disabling RLS temporarily to break infinite recursion' as step;

-- Temporarily disable RLS on user_profiles to break the recursion
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: DROP ALL PROBLEMATIC POLICIES
-- ============================================================================

SELECT 'STEP 2: Dropping all problematic user_profiles policies' as step;

-- Drop all existing policies that are causing recursion
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can manage all user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Public can view basic user info" ON user_profiles;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON user_profiles;

-- ============================================================================
-- STEP 3: CREATE HELPER FUNCTION TO AVOID RECURSION
-- ============================================================================

SELECT 'STEP 3: Creating helper function to check admin status safely' as step;

-- Create a security definer function to check admin status without recursion
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT role INTO user_role 
  FROM user_profiles 
  WHERE id = user_id;
  
  RETURN COALESCE(user_role = 'admin', false);
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO authenticated, anon;

-- ============================================================================
-- STEP 4: CREATE NON-RECURSIVE RLS POLICIES
-- ============================================================================

SELECT 'STEP 4: Creating safe, non-recursive RLS policies' as step;

-- Re-enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 1. Users can view their own profile (no recursion)
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- 2. Users can update their own profile (no recursion, no role change)
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (
    auth.uid() = id AND
    -- Prevent role changes by regular users
    (role IS NULL OR role = (
      SELECT up.role FROM user_profiles up WHERE up.id = auth.uid()
    ))
  );

-- 3. Users can insert their own profile (no recursion)
CREATE POLICY "Users can insert their own profile"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- 4. Admins can view all profiles (using safe function)
CREATE POLICY "Admins can view all user profiles"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

-- 5. Admins can manage all profiles (using safe function)
CREATE POLICY "Admins can manage all user profiles"
  ON user_profiles FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- ============================================================================
-- STEP 5: FIX OTHER TABLES THAT DEPEND ON USER_PROFILES
-- ============================================================================

SELECT 'STEP 5: Fixing other tables that depend on user_profiles' as step;

-- Fix orders policies to use the safe function
DROP POLICY IF EXISTS "Admins can view all orders" ON orders;
DROP POLICY IF EXISTS "Admins can manage all orders" ON orders;

CREATE POLICY "Admins can view all orders"
  ON orders FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

CREATE POLICY "Admins can manage all orders"
  ON orders FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- Fix order_items policies
DROP POLICY IF EXISTS "Admins can view all order items" ON order_items;
DROP POLICY IF EXISTS "Admins can manage all order items" ON order_items;

CREATE POLICY "Admins can view all order items"
  ON order_items FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

CREATE POLICY "Admins can manage all order items"
  ON order_items FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- Fix products policies
DROP POLICY IF EXISTS "Admins can view all products" ON products;
DROP POLICY IF EXISTS "Admins can manage products" ON products;

CREATE POLICY "Admins can view all products"
  ON products FOR SELECT
  TO authenticated
  USING (is_admin(auth.uid()));

CREATE POLICY "Admins can manage products"
  ON products FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- Fix categories policies
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;

CREATE POLICY "Admins can manage categories"
  ON categories FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- Fix product_images policies
DROP POLICY IF EXISTS "Admins can manage product images" ON product_images;

CREATE POLICY "Admins can manage product images"
  ON product_images FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- Fix product_reviews policies
DROP POLICY IF EXISTS "Admins can manage all reviews" ON product_reviews;

CREATE POLICY "Admins can manage all reviews"
  ON product_reviews FOR ALL
  TO authenticated
  USING (is_admin(auth.uid()));

-- ============================================================================
-- STEP 6: GRANT BASIC PERMISSIONS TO BREAK DEPENDENCY ISSUES
-- ============================================================================

SELECT 'STEP 6: Granting basic permissions to ensure access' as step;

-- Grant basic permissions to avoid permission errors
GRANT SELECT ON user_profiles TO authenticated, anon;
GRANT INSERT, UPDATE ON user_profiles TO authenticated;
GRANT SELECT ON orders TO authenticated;
GRANT SELECT ON order_items TO authenticated;
GRANT SELECT ON products TO authenticated, anon;
GRANT SELECT ON product_images TO authenticated, anon;
GRANT SELECT ON categories TO authenticated, anon;
GRANT SELECT ON product_reviews TO authenticated, anon;

-- ============================================================================
-- STEP 7: TEST THE FIX
-- ============================================================================

SELECT 'STEP 7: Testing the fix' as step;

-- Test basic queries that were failing
SELECT 'Testing user_profiles access:' as test;
SELECT COUNT(*) as user_profiles_count FROM user_profiles;

SELECT 'Testing products access:' as test;
SELECT COUNT(*) as products_count FROM products;

SELECT 'Testing orders access:' as test;
SELECT COUNT(*) as orders_count FROM orders;

SELECT 'Testing admin function:' as test;
SELECT is_admin(auth.uid()) as is_current_user_admin;

-- ============================================================================
-- FINAL RESULT
-- ============================================================================

SELECT '🎉 URGENT FIX COMPLETED! 🎉' as result;
SELECT 'Infinite recursion in user_profiles policies has been fixed!' as info;
SELECT 'All dependent services should now work properly.' as info2;
SELECT 'Please refresh your application and check the console.' as info3;
