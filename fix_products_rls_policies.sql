-- Fix Products Table RLS Policies
-- This script fixes the Row Level Security policies for the products table
-- to ensure products can be properly fetched by the frontend

-- ============================================================================
-- STEP 1: CHECK CURRENT STATE
-- ============================================================================

SELECT 'STEP 1: Checking current products table state' as step;

-- Check if products table exists and has data
SELECT 
  'products' as table_name,
  COUNT(*) as total_count,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
  COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
  COUNT(CASE WHEN status = 'deleted' THEN 1 END) as deleted_count
FROM products;

-- Check current RLS status
SELECT
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename = 'products';

-- List current policies on products table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'products';

-- ============================================================================
-- STEP 2: ENABLE RLS ON PRODUCTS TABLE
-- ============================================================================

SELECT 'STEP 2: Enabling RLS on products table' as step;

-- Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 3: DROP EXISTING POLICIES
-- ============================================================================

SELECT 'STEP 3: Dropping existing policies' as step;

-- Drop all existing policies on products table to start fresh
DROP POLICY IF EXISTS "Anyone can view active products" ON products;
DROP POLICY IF EXISTS "Anyone can view products" ON products;
DROP POLICY IF EXISTS "Public can view active products" ON products;
DROP POLICY IF EXISTS "Only admins can modify products" ON products;
DROP POLICY IF EXISTS "Admins can manage products" ON products;

-- ============================================================================
-- STEP 4: CREATE NEW POLICIES
-- ============================================================================

SELECT 'STEP 4: Creating new RLS policies' as step;

-- Allow EVERYONE (including anonymous users) to view active products
CREATE POLICY "Public can view active products"
  ON products FOR SELECT
  USING (status = 'active');

-- Allow authenticated users with admin role to view all products (including drafts)
CREATE POLICY "Admins can view all products"
  ON products FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Allow authenticated users with admin role to insert products
CREATE POLICY "Admins can insert products"
  ON products FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Allow authenticated users with admin role to update products
CREATE POLICY "Admins can update products"
  ON products FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Allow authenticated users with admin role to delete products
CREATE POLICY "Admins can delete products"
  ON products FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- STEP 5: ENSURE RELATED TABLES HAVE PROPER POLICIES
-- ============================================================================

SELECT 'STEP 5: Setting up related table policies' as step;

-- Enable RLS on categories table if not already enabled
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing category policies
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "Only admins can modify categories" ON categories;

-- Allow everyone to view categories
CREATE POLICY "Public can view categories"
  ON categories FOR SELECT
  USING (true);

-- Allow admins to manage categories
CREATE POLICY "Admins can manage categories"
  ON categories FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Enable RLS on product_images table if not already enabled
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;

-- Drop existing product_images policies
DROP POLICY IF EXISTS "Anyone can view product images" ON product_images;
DROP POLICY IF EXISTS "Public can view product images" ON product_images;

-- Allow everyone to view product images
CREATE POLICY "Public can view product images"
  ON product_images FOR SELECT
  USING (true);

-- Allow admins to manage product images
CREATE POLICY "Admins can manage product images"
  ON product_images FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- ============================================================================
-- STEP 6: VERIFICATION
-- ============================================================================

SELECT 'STEP 6: Verification' as step;

-- Check final RLS status
SELECT
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename IN ('products', 'categories', 'product_images')
ORDER BY tablename;

-- List final policies
SELECT
  tablename,
  policyname,
  cmd,
  roles
FROM pg_policies
WHERE tablename IN ('products', 'categories', 'product_images')
ORDER BY tablename, policyname;

-- Test query to see if products are accessible
SELECT 'Testing product access:' as test;
SELECT 
  id, 
  name, 
  price, 
  status,
  created_at
FROM products 
WHERE status = 'active'
ORDER BY created_at DESC 
LIMIT 3;

SELECT 'Products RLS policies have been successfully updated!' as result;
SELECT 'Products with status = ''active'' should now be visible to all users.' as info;
